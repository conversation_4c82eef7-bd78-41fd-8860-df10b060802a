import React, { useCallback, useState } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  Handle,
  Position,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { Search, Play, ExternalLink, ChevronDown, ChevronRight } from 'lucide-react';

// Clean Professional Node Component - Augment Style
const ProfessionalNode = ({ data, selected }: { data: any; selected: boolean }) => {
  const nodeColors: { [key: string]: { bg: string; border: string; accent: string } } = {
    'Chat Input': { bg: 'bg-blue-50', border: 'border-blue-200', accent: 'bg-blue-500' },
    'Chat Output': { bg: 'bg-blue-50', border: 'border-blue-200', accent: 'bg-blue-500' },
    'OpenAI': { bg: 'bg-green-50', border: 'border-green-200', accent: 'bg-green-500' },
    'OpenAI Embeddings': { bg: 'bg-green-50', border: 'border-green-200', accent: 'bg-green-500' },
    'Prompt': { bg: 'bg-orange-50', border: 'border-orange-200', accent: 'bg-orange-500' },
    'Chat Memory': { bg: 'bg-purple-50', border: 'border-purple-200', accent: 'bg-purple-500' },
  };

  const nodeStyle = nodeColors[data.type] || { bg: 'bg-gray-50', border: 'border-gray-200', accent: 'bg-gray-500' };

  return (
    <div className={`
      relative bg-white rounded-lg shadow-sm border transition-all duration-200
      ${selected ? 'border-blue-400 shadow-md' : `${nodeStyle.border} hover:shadow-md`}
      min-w-[200px] max-w-[280px]
    `}>
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white"
      />
      
      {/* Node Header */}
      <div className={`${nodeStyle.bg} px-4 py-3 rounded-t-lg border-b ${nodeStyle.border}`}>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${nodeStyle.accent}`}></div>
          <span className="font-medium text-sm text-gray-900">{data.type}</span>
        </div>
      </div>
      
      {/* Node Body */}
      <div className="px-4 py-3">
        <p className="text-xs text-gray-600 leading-relaxed">{data.description}</p>
      </div>
      
      {/* Status indicator */}
      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
    </div>
  );
};

const nodeTypes = {
  professional: ProfessionalNode,
};

const AxieFlowBuilder: React.FC = () => {
  // Professional workflow nodes
  const initialNodes: Node[] = [
    {
      id: 'chat-input',
      type: 'professional',
      position: { x: 275, y: 150 },
      data: { 
        type: 'Chat Input', 
        description: 'Handles customer messages and inquiries'
      },
    },
    {
      id: 'openai-embeddings',
      type: 'professional',
      position: { x: 275, y: 280 },
      data: { 
        type: 'OpenAI Embeddings', 
        description: 'Creates embeddings for knowledge retrieval'
      },
    },
    {
      id: 'chat-memory',
      type: 'professional',
      position: { x: 450, y: 200 },
      data: { 
        type: 'Chat Memory', 
        description: 'Maintains conversation context'
      },
    },
    {
      id: 'prompt-template',
      type: 'professional',
      position: { x: 600, y: 280 },
      data: { 
        type: 'Prompt', 
        description: 'Formats requests with company data'
      },
    },
    {
      id: 'openai-model',
      type: 'professional',
      position: { x: 750, y: 350 },
      data: { 
        type: 'OpenAI', 
        description: 'Generates intelligent responses'
      },
    },
    {
      id: 'chat-output',
      type: 'professional',
      position: { x: 900, y: 300 },
      data: { 
        type: 'Chat Output', 
        description: 'Delivers responses to customers'
      },
    },
  ];

  const initialEdges: Edge[] = [
    {
      id: 'e-chat-input-memory',
      source: 'chat-input',
      target: 'chat-memory',
      type: 'default',
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
    {
      id: 'e-embeddings-prompt',
      source: 'openai-embeddings',
      target: 'prompt-template',
      type: 'default',
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
    {
      id: 'e-memory-prompt',
      source: 'chat-memory',
      target: 'prompt-template',
      type: 'default',
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
    {
      id: 'e-prompt-openai',
      source: 'prompt-template',
      target: 'openai-model',
      type: 'default',
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
    {
      id: 'e-openai-output',
      source: 'openai-model',
      target: 'chat-output',
      type: 'default',
      style: { stroke: '#6b7280', strokeWidth: 2 },
    },
  ];

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Component categories
  const componentCategories = [
    {
      name: 'Input / Output',
      items: [
        { name: 'Chat Input', description: 'Handles user messages' },
        { name: 'Chat Output', description: 'Sends responses' },
        { name: 'Text Input', description: 'Text input field' },
        { name: 'Text Output', description: 'Text output display' },
      ]
    },
    {
      name: 'AI Models',
      items: [
        { name: 'OpenAI', description: 'OpenAI language model' },
        { name: 'Anthropic', description: 'Anthropic Claude model' },
        { name: 'OpenAI Embeddings', description: 'Text embeddings' },
      ]
    },
    {
      name: 'Data Processing',
      items: [
        { name: 'Prompt', description: 'Format prompts' },
        { name: 'Chat Memory', description: 'Store conversation history' },
        { name: 'Text Splitter', description: 'Split text into chunks' },
      ]
    },
  ];

  const addNode = (componentType: string, description: string) => {
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: 'professional',
      position: { x: Math.random() * 400 + 300, y: Math.random() * 300 + 200 },
      data: { type: componentType, description },
    };
    setNodes((nds) => [...nds, newNode]);
  };

  return (
    <div className="w-full bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
      <div className="w-full h-[800px] flex flex-col">
        {/* EXACT Langflow Header - As Is */}
        <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center text-white font-bold text-sm">
                AX
              </div>
              <span className="text-gray-500 text-sm font-medium">Starter Project</span>
            </div>
            <div className="flex items-center space-x-2 bg-red-50 px-3 py-1.5 rounded-full border border-red-200">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-red-700 font-medium text-sm">Axie Studio</span>
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <button
              onClick={() => window.open('https://axieagent.netlify.app', '_blank')}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <Play className="w-4 h-4" />
              <span className="text-sm font-medium">Playground</span>
            </button>
            <button
              onClick={() => window.open('https://ragsystem.axiestudio.se/', '_blank')}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
              <span className="text-sm font-medium">Share</span>
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Left Sidebar - Clean and Professional */}
          <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
            <div className="px-4 py-4 border-b border-gray-200">
              <h3 className="font-semibold text-gray-900 text-sm mb-3">Components</h3>
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white"
                />
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              {componentCategories.map((category, idx) => (
                <div key={idx} className="border-b border-gray-200 last:border-b-0">
                  <div 
                    className="flex items-center justify-between px-4 py-3 text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-100 transition-colors"
                    onClick={() => setSelectedCategory(selectedCategory === category.name ? null : category.name)}
                  >
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 text-gray-500">
                        {selectedCategory === category.name ? 
                          <ChevronDown className="w-4 h-4" /> : 
                          <ChevronRight className="w-4 h-4" />
                        }
                      </div>
                      <span>{category.name}</span>
                    </div>
                  </div>
                  {selectedCategory === category.name && (
                    <div className="bg-white border-t border-gray-100">
                      {category.items.map((item, itemIdx) => (
                        <div
                          key={itemIdx}
                          className="px-8 py-2 text-sm text-gray-600 cursor-pointer hover:bg-blue-50 hover:text-blue-700 transition-colors border-l-2 border-transparent hover:border-blue-200"
                          onClick={() => addNode(item.name, item.description)}
                        >
                          {item.name}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Main Flow Canvas */}
          <div className="flex-1 relative bg-gray-50">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              nodeTypes={nodeTypes}
              fitView
              className="bg-gray-50"
              defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
              minZoom={0.1}
              maxZoom={2}
            >
              <Background 
                variant={BackgroundVariant.Dots} 
                gap={20} 
                size={1} 
                color="#e5e7eb"
              />
              <Controls 
                className="bg-white border border-gray-200 rounded-lg shadow-sm"
                showInteractive={false}
              />
            </ReactFlow>
            
            {/* Status indicator */}
            <div className="absolute bottom-4 left-4 flex items-center space-x-2">
              <div className="bg-white border border-gray-200 rounded-lg px-3 py-2 shadow-sm">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Flow Ready</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Clean Status */}
          <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
            <div className="px-4 py-4 border-b border-gray-200">
              <h3 className="font-semibold text-gray-900 text-sm">Status</h3>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <div className="text-gray-900 font-medium">Flow initialized</div>
                    <div className="text-gray-500 text-xs">Customer service workflow ready</div>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <div className="text-gray-900 font-medium">Components loaded</div>
                    <div className="text-gray-500 text-xs">All nodes operational</div>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <div className="text-gray-900 font-medium">Ready for input</div>
                    <div className="text-gray-500 text-xs">Waiting for customer messages</div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="space-y-2">
                  <button 
                    onClick={() => window.open('https://axieagent.netlify.app', '_blank')}
                    className="w-full text-left px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors border border-blue-200"
                  >
                    Test AI Agent
                  </button>
                  <button 
                    onClick={() => window.open('https://ragsystem.axiestudio.se/', '_blank')}
                    className="w-full text-left px-3 py-2 text-sm bg-green-50 text-green-700 rounded-md hover:bg-green-100 transition-colors border border-green-200"
                  >
                    Upload Files
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AxieFlowBuilder;
