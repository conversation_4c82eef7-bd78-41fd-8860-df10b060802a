@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --border: 214.3 31.8% 91.4%;
    color-scheme: light only;
  }
  
  * {
    @apply border-border;
    color-scheme: light only;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    overflow-x: hidden;
    color-scheme: light only;
    background-color: white !important;
    color: #1f2937 !important;
  }
  
  html {
    scroll-behavior: smooth;
    color-scheme: light only;
    background-color: white !important;
  }
}

@layer utilities {
  /* Simple animations */
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .text-balance {
    text-wrap: balance;
  }
  
  /* Simple gradient text */
  .gradient-text-cosmic {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent;
  }
  
  .gradient-text-aurora {
    @apply bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent;
  }
  
  /* Glass effect */
  .glass-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }
  
  /* Simple hover effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .hover-lift:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
  
  /* Shadow effects */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-10px); 
  }
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible {
  @apply outline-2 outline-offset-2 outline-blue-500;
}

/* Premium chat animations */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-10px); 
  }
}

@keyframes floating-delayed {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  50% { 
    transform: translateY(-15px) rotate(2deg); 
  }
}

@keyframes floating-slow {
  0%, 100% { 
    transform: translateY(0px) scale(1); 
  }
  50% { 
    transform: translateY(-8px) scale(1.02); 
  }
}

.animate-floating-delayed {
  animation: floating-delayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-floating-slow {
  animation: floating-slow 12s ease-in-out infinite;
  animation-delay: 4s;
}

/* Premium gradient animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Enhanced glass effects */
.glass-card-premium {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Premium button effects */
.btn-premium {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.btn-premium:hover::before {
  left: 100%;
}

/* Enhanced scrollbar styling */
.premium-scroll::-webkit-scrollbar {
  width: 6px;
}

.premium-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.premium-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 3px;
}

.premium-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #7c3aed);
}

/* Improved mobile touch targets */
@media (max-width: 768px) {
  button,
  a {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Improve mobile scrolling */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
  
  /* Better mobile menu spacing */
  .mobile-menu-item {
    padding: 12px 16px;
    margin: 4px 0;
  }
}

/* Tablet specific improvements */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-optimized {
    padding: 16px;
  }
  
  .tablet-modal {
    max-height: 80vh;
  }
}

/* Enhanced scrollbar for mobile */
@media (max-width: 768px) {
  /* Add bottom padding to prevent content from being hidden behind bottom nav */
  body {
    padding-bottom: env(safe-area-inset-bottom, 0px);
  }
  
  /* Add bottom margin to main content on mobile */
  main, section:last-of-type {
    margin-bottom: 80px;
  }
  
  ::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: transparent;
  }
  
  /* For Firefox on mobile */
  * {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
}

/* Safe area support for devices with notches */
.h-safe-area-inset-bottom {
  height: env(safe-area-inset-bottom, 0px);
}

/* Native app feel improvements */
@media (max-width: 768px) {
  /* Prevent text selection on navigation elements */
  .bottom-nav {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }
  
  /* Improve touch targets */
  button, a {
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Smooth scrolling for mobile */
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}

/* Desktop scrollbar - invisible but functional */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
}

::-webkit-scrollbar-thumb:hover {
  background: transparent;
}

/* Force light mode for all elements */
*, *::before, *::after {
  color-scheme: light only !important;
}

/* Specific overrides for common dark mode elements */
input, textarea, select, button {
  color-scheme: light only !important;
  background-color: white !important;
}

/* Override system dark mode preferences */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: light only !important;
    --background: 0 0% 100% !important;
    --foreground: 222.2 84% 4.9% !important;
  }
  
  html {
    background-color: white !important;
    color-scheme: light only !important;
  }
  
  body {
    background-color: white !important;
    color: #1f2937 !important;
    color-scheme: light only !important;
  }
  
  * {
    color-scheme: light only !important;
  }
}

/* Mobile and tablet specific overrides */
@media (max-width: 1024px) {
  html, body {
    background-color: white !important;
    color: #1f2937 !important;
    color-scheme: light only !important;
  }
  
  * {
    color-scheme: light only !important;
  }
  
  /* Invisible scrollbar for mobile/tablet */
  ::-webkit-scrollbar {
    display: none;
    width: 0px;
    background: transparent;
  }
  
  /* Firefox mobile */
  * {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
}

/* iOS Safari specific */
@supports (-webkit-touch-callout: none) {
  html, body {
    background-color: white !important;
    color: #1f2937 !important;
    color-scheme: light only !important;
  }
  
  ::-webkit-scrollbar {
    display: none;
  }
}

/* Android Chrome specific */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  html, body {
    background-color: white !important;
    color: #1f2937 !important;
    color-scheme: light only !important;
  }
  
  ::-webkit-scrollbar {
    display: none;
    width: 0px;
  }
}

/* Better typography */
h1, h2, h3, h4, h5, h6 {
  text-wrap: balance;
  line-height: 1.2;
  font-weight: 700;
}

p {
  text-wrap: pretty;
  line-height: 1.6;
}

/* Simple button styles */
.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-4 px-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105;
}

.btn-secondary {
  @apply border-2 border-blue-600 text-blue-600 font-semibold py-4 px-8 rounded-2xl hover:bg-blue-600 hover:text-white transition-all duration-300 transform hover:scale-105;
  background: rgba(255, 255, 255, 0.9);
}

/* Responsive design */
@media (max-width: 640px) {
  .text-responsive {
    font-size: clamp(1rem, 4vw, 1.5rem);
  }
  
  .heading-responsive {
    font-size: clamp(1.5rem, 8vw, 3rem);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}