<!DOCTYPE html>
<html lang="sv">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Offline - Axie Studio</title>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #f0f4f8, #d9e2ec);
      color: #334e68;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    
    .container {
      max-width: 500px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.5);
    }
    
    .logo {
      width: 80px;
      height: 80px;
      border-radius: 16px;
      margin-bottom: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      font-size: 28px;
      margin-bottom: 16px;
      color: #102a43;
    }
    
    p {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 24px;
      color: #486581;
    }
    
    .icon {
      font-size: 64px;
      margin-bottom: 20px;
    }
    
    .button {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
      margin-top: 16px;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    }
    
    .status {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 32px;
      font-size: 14px;
      color: #627d98;
    }
    
    .status-dot {
      width: 10px;
      height: 10px;
      background-color: #ef4444;
      border-radius: 50%;
      margin-right: 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="https://kt5xwoxw7ivvaxql.public.blob.vercel-storage.com/axie-studio-logo.png-KXUZ7kPWDExqtd3vBbevDyEnUzu8Il.jpeg" alt="Axie Studio" class="logo">
    <img src="/logo.jpg" alt="Axie Studio" class="logo">
    <div class="icon">🤖</div>
    <h1>AI Assistant Offline</h1>
    <p>Our AI-powered services are temporarily unavailable. Please check your connection and try again.</p>
    <p>Axie Studio's intelligent features require an internet connection to provide you with personalized AI assistance.</p>
    <button class="button" onclick="window.location.reload()">Try Again</button>
    
    <div class="status">
      <div class="status-dot"></div>
      <span>AI Services Offline</span>
    </div>
  </div>
  
  <script>
    // Check if we're back online
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html>