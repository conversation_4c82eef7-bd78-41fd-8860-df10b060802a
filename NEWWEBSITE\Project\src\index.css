@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --border: 214.3 31.8% 91.4%;
    color-scheme: light only;
  }

  * {
    color-scheme: light only;
  }

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    overflow-x: hidden;
    color-scheme: light only;
    background-color: white !important;
    color: #1f2937 !important;
  }

  html {
    scroll-behavior: smooth;
    color-scheme: light only;
    background-color: white !important;
  }
}

@layer utilities {
  /* Advanced Professional Animations */
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulseSlow 3s ease-in-out infinite;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 2s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  .animate-typewriter {
    animation: typewriter 3s steps(40) 1s forwards;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Advanced Gradient Text Effects */
  .gradient-text-cosmic {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
  }

  .gradient-text-aurora {
    @apply bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradientShift 4s ease infinite;
  }

  .gradient-text-fire {
    @apply bg-gradient-to-r from-orange-400 via-red-500 to-pink-600 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradientShift 2s ease infinite;
  }

  /* Advanced Glass Effects */
  .glass-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card-dark {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }

  .glass-morphism {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }

  /* Professional Hover Effects */
  .hover-lift {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .hover-tilt {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-tilt:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg) scale(1.02);
  }

  .hover-glow {
    transition: all 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
    transform: translateY(-2px);
  }

  /* Advanced Shadow Effects */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
  }

  .shadow-glow-green {
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.4);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 30px rgba(147, 51, 234, 0.4);
  }

  .shadow-neumorphism {
    box-shadow: 20px 20px 60px #bebebe, -20px -20px 60px #ffffff;
  }

  .shadow-neumorphism-inset {
    box-shadow: inset 20px 20px 60px #bebebe, inset -20px -20px 60px #ffffff;
  }

  /* Professional Loading States */
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  .loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
  }

  /* Professional Scroll Indicators */
  .scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    transform-origin: left;
    z-index: 9999;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
