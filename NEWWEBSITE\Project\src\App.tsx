import React, { useState } from 'react';
import { <PERSON>u, X, <PERSON>R<PERSON>, ExternalLink, Github } from 'lucide-react';
import AxieFlowBuilder from './components/AxieFlowBuilder';

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <div className="min-h-screen bg-white">
      {/* Professional Header - Industry Leading Design */}
      <header className="sticky top-0 z-50 border-b border-gray-200 bg-white/95 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="relative group">
                <img src="/logo.jpg" alt="Axie Studio" className="h-8 w-8 mr-3 transition-transform group-hover:scale-105" />
                <div className="absolute inset-0 bg-blue-500/20 rounded-full scale-0 group-hover:scale-110 transition-transform duration-300"></div>
              </div>
              <span className="text-xl font-semibold text-gray-900">Axie Studio</span>
              <div className="ml-3 px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                Live
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8">
              <a href="#workflow" className="text-sm text-gray-600 hover:text-gray-900 transition-colors font-medium">Workflow Builder</a>
              <a href="#features" className="text-sm text-gray-600 hover:text-gray-900 transition-colors font-medium">Features</a>
              <a href="#metrics" className="text-sm text-gray-600 hover:text-gray-900 transition-colors font-medium">Performance</a>
              <a href="#contact" className="text-sm text-gray-600 hover:text-gray-900 transition-colors font-medium">Get Started</a>
              <div className="w-px h-4 bg-gray-300"></div>
              <a
                href="https://github.com/OGGsd/Vercel-Frontend-"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-gray-600 hover:text-gray-900 transition-colors flex items-center font-medium"
              >
                <Github className="w-4 h-4 mr-1" />
                Source
              </a>
              <a
                href="https://axieagent.netlify.app"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors text-sm font-medium"
              >
                Live Demo
              </a>
            </nav>

            <button
              className="md:hidden p-2 hover:bg-gray-100 rounded-md transition-colors"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>

          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-200 bg-white">
              <nav className="flex flex-col space-y-4">
                <a href="#features" className="text-sm text-gray-600 hover:text-gray-900 transition-colors font-medium">Features</a>
                <a href="#workflow" className="text-sm text-gray-600 hover:text-gray-900 transition-colors font-medium">Workflow</a>
                <a href="#metrics" className="text-sm text-gray-600 hover:text-gray-900 transition-colors font-medium">Metrics</a>
                <a href="#contact" className="text-sm text-gray-600 hover:text-gray-900 transition-colors font-medium">Contact</a>
                <div className="border-t border-gray-200 pt-4">
                  <a
                    href="https://github.com/OGGsd/Vercel-Frontend-"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-600 hover:text-gray-900 transition-colors flex items-center font-medium mb-3"
                  >
                    <Github className="w-4 h-4 mr-2" />
                    View Source Code
                  </a>
                  <a
                    href="https://axieagent.netlify.app"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors text-sm font-medium inline-block"
                  >
                    Try Demo
                  </a>
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section - Workflow Builder as Main Product */}
      <section className="relative bg-gradient-to-b from-white to-gray-50 py-32 overflow-hidden">
        {/* Subtle background elements */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl">
            {/* Professional Badge - Workflow Builder Focus */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 text-red-700 text-sm font-medium mb-8 hover:shadow-md transition-all duration-300">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></div>
              Industry-leading visual workflow builder
              <ArrowRight className="w-4 h-4 ml-2 opacity-60" />
            </div>

            {/* Workflow Builder Hero */}
            <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight tracking-tight text-gray-900">
              Visual workflow builder.
              <br />
              <span className="text-gray-400">Drag, drop, deploy.</span>
              <br />
              <span className="bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
                That simple.
              </span>
            </h1>

            <p className="text-xl text-gray-600 mb-12 max-w-3xl leading-relaxed">
              Build powerful AI workflows with our visual drag-and-drop interface.
              Connect AI models, data sources, and business logic without writing code.
              Deploy instantly with our integrated RAG system and live AI agents.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mb-16">
              <button
                onClick={() => document.getElementById('workflow')?.scrollIntoView({ behavior: 'smooth' })}
                className="group bg-black text-white px-8 py-4 rounded-md hover:bg-gray-800 transition-all duration-300 font-medium text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <span className="flex items-center">
                  Try the workflow builder
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </span>
              </button>
              <button
                onClick={() => window.open('https://axieagent.netlify.app', '_blank')}
                className="group border-2 border-gray-300 text-gray-900 px-8 py-4 rounded-md hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 font-medium text-lg flex items-center justify-center"
              >
                See live example
                <ExternalLink className="w-5 h-5 ml-2 group-hover:scale-110 transition-transform" />
              </button>
            </div>

            {/* Workflow Builder Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-200">
              <div className="text-center group">
                <div className="text-3xl font-bold text-gray-900 mb-2 group-hover:text-red-600 transition-colors">50+</div>
                <div className="text-sm text-gray-600 font-medium">AI Components</div>
                <div className="text-xs text-gray-500 mt-1">Ready to use</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">Free</div>
                <div className="text-sm text-gray-600 font-medium">RAG System</div>
                <div className="text-xs text-gray-500 mt-1">Upload your data</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">Live</div>
                <div className="text-sm text-gray-600 font-medium">Deployment</div>
                <div className="text-xs text-gray-500 mt-1">Instant publishing</div>
              </div>
            </div>

            {/* Direct Access to Tools */}
            <div className="mt-12 flex flex-wrap gap-4 justify-center">
              <a
                href="https://axieagent.netlify.app"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-blue-50 text-blue-700 rounded-full text-sm font-medium hover:bg-blue-100 transition-colors"
              >
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                Live AI Agent
              </a>
              <a
                href="https://ragsystem.axiestudio.se/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm font-medium hover:bg-green-100 transition-colors"
              >
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                RAG File System
              </a>
              <a
                href="https://github.com/OGGsd/Vercel-Frontend-"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-gray-50 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-100 transition-colors"
              >
                <Github className="w-3 h-3 mr-2" />
                Open Source
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Main Product Showcase - Workflow Builder */}
      <section id="workflow" className="bg-white py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 text-red-700 text-sm font-medium mb-8">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></div>
              Live Langflow Interface
              <div className="ml-2 px-2 py-0.5 bg-red-100 text-red-600 text-xs rounded-full">
                Interactive
              </div>
            </div>

            <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-8 leading-tight">
              Your workflow builder
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto mb-8">
              This is our actual Langflow interface - the same tool we use to build AI workflows.
              Drag components, connect nodes, and deploy instantly. No mockups, no demos - this is the real thing.
            </p>

            {/* Feature badges */}
            <div className="flex flex-wrap justify-center gap-3 mb-12">
              <div className="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm font-medium">
                Real Langflow
              </div>
              <div className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                Drag & Drop
              </div>
              <div className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                Live Deployment
              </div>
              <div className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium">
                AI Components
              </div>
              <div className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm font-medium">
                RAG Integration
              </div>
            </div>
          </div>

          {/* Your Actual Langflow Interface - Main Product */}
          <div className="relative">
            {/* Subtle glow effect */}
            <div className="absolute -inset-4 bg-gradient-to-r from-red-500/10 via-orange-500/10 to-yellow-500/10 rounded-2xl blur-xl"></div>
            <div className="relative">
              <AxieFlowBuilder />
            </div>
          </div>

          {/* Professional Action Bar */}
          <div className="mt-12 bg-gray-50 rounded-xl p-8 border border-gray-200">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">This is what you get</h3>
              <p className="text-gray-600">Full access to our workflow builder and all integrated tools</p>
            </div>

            <div className="grid md:grid-cols-3 gap-4">
              <button
                onClick={() => window.open('https://github.com/OGGsd/Vercel-Frontend-', '_blank')}
                className="group bg-black text-white px-6 py-4 rounded-lg hover:bg-gray-800 transition-all duration-300 font-medium flex items-center justify-center transform hover:-translate-y-0.5 hover:shadow-lg"
              >
                <Github className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="font-semibold">Source Code</div>
                  <div className="text-xs opacity-80">Open source Langflow</div>
                </div>
              </button>

              <button
                onClick={() => window.open('https://ragsystem.axiestudio.se/', '_blank')}
                className="group bg-green-600 text-white px-6 py-4 rounded-lg hover:bg-green-700 transition-all duration-300 font-medium flex items-center justify-center transform hover:-translate-y-0.5 hover:shadow-lg"
              >
                <svg className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <div className="text-left">
                  <div className="font-semibold">Upload Files</div>
                  <div className="text-xs opacity-80">Free RAG System</div>
                </div>
              </button>

              <button
                onClick={() => window.open('https://axieagent.netlify.app', '_blank')}
                className="group bg-blue-600 text-white px-6 py-4 rounded-lg hover:bg-blue-700 transition-all duration-300 font-medium flex items-center justify-center transform hover:-translate-y-0.5 hover:shadow-lg"
              >
                <svg className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <div className="text-left">
                  <div className="font-semibold">Live Example</div>
                  <div className="text-xs opacity-80">AI Agent Demo</div>
                </div>
              </button>
            </div>

            {/* Usage stats */}
            <div className="mt-8 pt-6 border-t border-gray-200 grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-gray-900">50+</div>
                <div className="text-sm text-gray-600">AI Components</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">Free</div>
                <div className="text-sm text-gray-600">RAG System</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">Live</div>
                <div className="text-sm text-gray-600">Deployment</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Supporting Features Section */}
      <section id="features" className="bg-gray-50 py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Everything you need to build AI workflows
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              Our workflow builder comes with integrated tools, AI components, and deployment options.
              Plus access to our legacy services for complete digital solutions.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="group bg-white rounded-xl p-8 shadow-sm border border-gray-200 hover:shadow-lg hover:border-red-200 transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-14 h-14 bg-gradient-to-br from-red-100 to-red-200 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-7 h-7 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 group-hover:text-red-600 transition-colors">50+ AI Components</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Drag and drop from our library of AI components. OpenAI, Anthropic, embeddings,
                vector stores, and custom logic blocks - all ready to use.
              </p>
              <div className="flex items-center justify-between">
                <div className="text-sm text-red-600 font-medium">
                  Models • Memory • Processing • I/O
                </div>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
            </div>

            <div className="group bg-white rounded-xl p-8 shadow-sm border border-gray-200 hover:shadow-lg hover:border-green-200 transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-14 h-14 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-7 h-7 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 group-hover:text-green-600 transition-colors">Free RAG System</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Upload your company files and documents. Your AI workflows will access this knowledge instantly
                with advanced retrieval-augmented generation technology.
              </p>
              <div className="flex items-center justify-between">
                <a
                  href="https://ragsystem.axiestudio.se/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-green-600 font-medium hover:text-green-700 transition-colors flex items-center group"
                >
                  Try RAG system
                  <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </a>
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              </div>
            </div>

            <div className="group bg-white rounded-xl p-8 shadow-sm border border-gray-200 hover:shadow-lg hover:border-blue-200 transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">Instant Deployment</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Build your workflow and deploy instantly. No complex setup, no server management.
                Your AI agents go live immediately with our hosting infrastructure.
              </p>
              <div className="flex items-center justify-between">
                <a
                  href="https://axieagent.netlify.app"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 font-medium hover:text-blue-700 transition-colors flex items-center group"
                >
                  See live example
                  <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </a>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Legacy Services - As Extras */}
          <div className="mt-16">
            <div className="text-center mb-12">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Plus our legacy services</h3>
              <p className="text-gray-600">Need more than just AI workflows? We also offer complete digital solutions.</p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-100">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Complete Digital Solutions</h4>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    Professional websites (14 days average)
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    Mobile apps & e-commerce
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    Booking systems & calendars
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    24/7 personal support
                  </li>
                </ul>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-8 border border-green-100">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Enterprise Features</h4>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    Open source & customizable
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    REST API & webhooks
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    Self-hosted options
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    Free consultation & setup
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Workflow Builder Showcase - Deep Dive */}
      <section className="bg-white py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              How our workflow builder works
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
              From concept to deployment in minutes. See how professionals build AI workflows
              with our visual interface - no coding required.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            {/* Left: Process Steps */}
            <div className="space-y-8">
              <div className="flex items-start">
                <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mr-6 flex-shrink-0">
                  <div className="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Drag & Drop Components</h3>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    Choose from 50+ AI components including OpenAI, Anthropic, embeddings, vector stores,
                    and custom logic blocks. Simply drag them onto the canvas.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-2 py-1 bg-red-50 text-red-700 text-xs rounded-full">OpenAI</span>
                    <span className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">Embeddings</span>
                    <span className="px-2 py-1 bg-green-50 text-green-700 text-xs rounded-full">Memory</span>
                    <span className="px-2 py-1 bg-purple-50 text-purple-700 text-xs rounded-full">Logic</span>
                  </div>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-6 flex-shrink-0">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Connect & Configure</h3>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    Draw connections between components to create your workflow logic.
                    Configure each component with your specific parameters and data sources.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">Visual Connections</span>
                    <span className="px-2 py-1 bg-green-50 text-green-700 text-xs rounded-full">Real-time Config</span>
                  </div>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-6 flex-shrink-0">
                  <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Test & Deploy</h3>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    Test your workflow in real-time with our integrated testing environment.
                    When ready, deploy instantly to our hosting infrastructure.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-2 py-1 bg-green-50 text-green-700 text-xs rounded-full">Live Testing</span>
                    <span className="px-2 py-1 bg-purple-50 text-purple-700 text-xs rounded-full">Instant Deploy</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right: Visual Representation */}
            <div className="relative">
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8 border border-gray-200">
                <div className="space-y-6">
                  {/* Mock workflow visualization */}
                  <div className="flex items-center justify-between">
                    <div className="w-20 h-12 bg-blue-100 rounded-lg flex items-center justify-center border-2 border-blue-200">
                      <span className="text-xs font-medium text-blue-700">Input</span>
                    </div>
                    <div className="flex-1 h-0.5 bg-gray-300 mx-4 relative">
                      <div className="absolute right-0 top-0 w-2 h-2 bg-gray-400 rounded-full transform translate-x-1 -translate-y-0.75"></div>
                    </div>
                    <div className="w-20 h-12 bg-green-100 rounded-lg flex items-center justify-center border-2 border-green-200">
                      <span className="text-xs font-medium text-green-700">AI</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-center">
                    <div className="w-0.5 h-8 bg-gray-300"></div>
                  </div>

                  <div className="flex items-center justify-center">
                    <div className="w-24 h-12 bg-purple-100 rounded-lg flex items-center justify-center border-2 border-purple-200">
                      <span className="text-xs font-medium text-purple-700">Memory</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-center">
                    <div className="w-0.5 h-8 bg-gray-300"></div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="w-20 h-12 bg-orange-100 rounded-lg flex items-center justify-center border-2 border-orange-200">
                      <span className="text-xs font-medium text-orange-700">RAG</span>
                    </div>
                    <div className="flex-1 h-0.5 bg-gray-300 mx-4 relative">
                      <div className="absolute right-0 top-0 w-2 h-2 bg-gray-400 rounded-full transform translate-x-1 -translate-y-0.75"></div>
                    </div>
                    <div className="w-20 h-12 bg-blue-100 rounded-lg flex items-center justify-center border-2 border-blue-200">
                      <span className="text-xs font-medium text-blue-700">Output</span>
                    </div>
                  </div>
                </div>

                <div className="mt-6 text-center">
                  <div className="inline-flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                    Workflow Active
                  </div>
                </div>
              </div>

              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold animate-bounce">
                AI
              </div>
              <div className="absolute -bottom-4 -left-4 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold animate-pulse">
                ⚡
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-orange-600 text-white rounded-lg hover:from-red-700 hover:to-orange-700 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer"
                 onClick={() => document.getElementById('workflow')?.scrollIntoView({ behavior: 'smooth' })}>
              <span className="flex items-center">
                Try the workflow builder now
                <ArrowRight className="w-5 h-5 ml-2" />
              </span>
            </div>
            <p className="text-sm text-gray-500 mt-3">No signup required • Start building immediately</p>
          </div>
        </div>
      </section>



      {/* Competitive Advantage Section */}
      <section className="bg-gray-50 py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Why choose our workflow builder?
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
              Compare us to other solutions. We provide the complete package -
              visual builder, free RAG system, instant deployment, and ongoing support.
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            {/* Comparison Table */}
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200">
              <div className="grid grid-cols-4 gap-0">
                {/* Header */}
                <div className="bg-gray-50 p-6 border-r border-gray-200">
                  <h3 className="font-semibold text-gray-900">Features</h3>
                </div>
                <div className="bg-gradient-to-br from-red-50 to-orange-50 p-6 border-r border-gray-200 text-center">
                  <div className="font-bold text-red-700 mb-2">Axie Studio</div>
                  <div className="text-sm text-red-600">Our Solution</div>
                </div>
                <div className="bg-gray-50 p-6 border-r border-gray-200 text-center">
                  <div className="font-semibold text-gray-700 mb-2">Competitor A</div>
                  <div className="text-sm text-gray-500">Code-based</div>
                </div>
                <div className="bg-gray-50 p-6 text-center">
                  <div className="font-semibold text-gray-700 mb-2">Competitor B</div>
                  <div className="text-sm text-gray-500">SaaS Platform</div>
                </div>

                {/* Visual Builder */}
                <div className="p-6 border-r border-t border-gray-200 bg-white">
                  <div className="font-medium text-gray-900">Visual Drag & Drop</div>
                  <div className="text-sm text-gray-500 mt-1">No coding required</div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center bg-green-50">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6 border-t border-gray-200 text-center">
                  <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mx-auto">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>

                {/* Free RAG System */}
                <div className="p-6 border-r border-t border-gray-200 bg-white">
                  <div className="font-medium text-gray-900">Free RAG System</div>
                  <div className="text-sm text-gray-500 mt-1">Upload your data</div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center bg-green-50">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6 border-t border-gray-200 text-center">
                  <div className="text-sm text-gray-500">$99/month</div>
                </div>

                {/* Instant Deployment */}
                <div className="p-6 border-r border-t border-gray-200 bg-white">
                  <div className="font-medium text-gray-900">Instant Deployment</div>
                  <div className="text-sm text-gray-500 mt-1">One-click publishing</div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center bg-green-50">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center">
                  <div className="text-sm text-gray-500">Manual setup</div>
                </div>
                <div className="p-6 border-t border-gray-200 text-center">
                  <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mx-auto">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>

                {/* Open Source */}
                <div className="p-6 border-r border-t border-gray-200 bg-white">
                  <div className="font-medium text-gray-900">Open Source</div>
                  <div className="text-sm text-gray-500 mt-1">Full code access</div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center bg-green-50">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6 border-t border-gray-200 text-center">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>

                {/* Support */}
                <div className="p-6 border-r border-t border-gray-200 bg-white rounded-bl-2xl">
                  <div className="font-medium text-gray-900">24/7 Support</div>
                  <div className="text-sm text-gray-500 mt-1">Personal assistance</div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center bg-green-50">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="p-6 border-r border-t border-gray-200 text-center">
                  <div className="text-sm text-gray-500">Community only</div>
                </div>
                <div className="p-6 border-t border-gray-200 text-center rounded-br-2xl">
                  <div className="text-sm text-gray-500">Business hours</div>
                </div>
              </div>
            </div>

            {/* Bottom CTA */}
            <div className="text-center mt-12">
              <div className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-red-600 to-orange-600 text-white rounded-lg hover:from-red-700 hover:to-orange-700 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 cursor-pointer"
                   onClick={() => document.getElementById('workflow')?.scrollIntoView({ behavior: 'smooth' })}>
                <span className="flex items-center">
                  Start building with our workflow builder
                  <ArrowRight className="w-5 h-5 ml-2" />
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-3">Free to start • No credit card • Full access</p>
            </div>
          </div>
        </div>
      </section>

      {/* Industry Leading Metrics Section */}
      <section id="metrics" className="bg-gradient-to-br from-gray-50 to-gray-100 py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 text-green-700 text-sm font-medium mb-8">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
              Proven Results
              <div className="ml-2 px-2 py-0.5 bg-green-100 text-green-600 text-xs rounded-full">
                Verified
              </div>
            </div>

            <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-8 leading-tight">
              Industry leading quality
            </h2>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-4xl mx-auto">
              Proprietary context retrieval combined with cutting-edge AI models deliver
              customer service you can trust. Real metrics from real businesses.
            </p>
            <a
              href="#"
              className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors group"
            >
              Read more: How RAG systems revolutionize customer support
              <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
            </a>
          </div>

          {/* Enhanced Metrics Dashboard */}
          <div className="max-w-6xl mx-auto">
            <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-200 overflow-hidden relative">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500"></div>
              </div>

              <div className="relative">
                <div className="text-center mb-12">
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">Axie Studio Performance Dashboard</h3>
                  <p className="text-gray-600">Real-time metrics from customer deployments</p>
                </div>

                {/* Main Metrics Grid */}
                <div className="grid md:grid-cols-3 gap-8 mb-12">
                  <div className="text-center group">
                    <div className="relative mb-4">
                      <div className="w-24 h-24 mx-auto bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div className="text-4xl font-bold text-green-600">85%</div>
                      </div>
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div className="font-semibold text-gray-900 mb-2">Faster Response Time</div>
                    <div className="text-sm text-gray-600 mb-3">Average improvement across all deployments</div>
                    <div className="text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full inline-block">
                      ↑ 12% this month
                    </div>
                  </div>

                  <div className="text-center group">
                    <div className="relative mb-4">
                      <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div className="text-3xl font-bold text-blue-600">24/7</div>
                      </div>
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <div className="font-semibold text-gray-900 mb-2">Always Available</div>
                    <div className="text-sm text-gray-600 mb-3">99.9% uptime with global infrastructure</div>
                    <div className="text-xs text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full inline-block">
                      Live monitoring
                    </div>
                  </div>

                  <div className="text-center group">
                    <div className="relative mb-4">
                      <div className="w-24 h-24 mx-auto bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div className="text-4xl font-bold text-purple-600">95%</div>
                      </div>
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </div>
                    </div>
                    <div className="font-semibold text-gray-900 mb-2">Customer Satisfaction</div>
                    <div className="text-sm text-gray-600 mb-3">Based on 10,000+ customer interactions</div>
                    <div className="text-xs text-purple-600 font-medium bg-purple-50 px-2 py-1 rounded-full inline-block">
                      ⭐ 4.8/5 rating
                    </div>
                  </div>
                </div>

                {/* Additional Metrics */}
                <div className="grid md:grid-cols-4 gap-6 pt-8 border-t border-gray-200">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">50+</div>
                    <div className="text-sm text-gray-600">AI Components</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">1M+</div>
                    <div className="text-sm text-gray-600">Messages Processed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">100+</div>
                    <div className="text-sm text-gray-600">Businesses Served</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">Free</div>
                    <div className="text-sm text-gray-600">RAG System</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Trust & Contact Section */}
      <section id="contact" className="bg-white py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Trust Indicators */}
          <div className="text-center mb-20">
            <p className="text-gray-500 mb-8 text-lg">Trusted by businesses across industries</p>
            <div className="grid grid-cols-2 md:grid-cols-6 gap-8 opacity-60">
              <div className="text-center group">
                <div className="w-16 h-16 mx-auto bg-blue-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-blue-200 transition-colors">
                  <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                </div>
                <div className="text-sm font-medium text-gray-600">E-commerce</div>
              </div>
              <div className="text-center group">
                <div className="w-16 h-16 mx-auto bg-green-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-green-200 transition-colors">
                  <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-sm font-medium text-gray-600">SaaS</div>
              </div>
              <div className="text-center group">
                <div className="w-16 h-16 mx-auto bg-red-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-red-200 transition-colors">
                  <svg className="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="text-sm font-medium text-gray-600">Healthcare</div>
              </div>
              <div className="text-center group">
                <div className="w-16 h-16 mx-auto bg-yellow-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-yellow-200 transition-colors">
                  <svg className="w-8 h-8 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
                  </svg>
                </div>
                <div className="text-sm font-medium text-gray-600">Finance</div>
              </div>
              <div className="text-center group">
                <div className="w-16 h-16 mx-auto bg-purple-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-purple-200 transition-colors">
                  <svg className="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                  </svg>
                </div>
                <div className="text-sm font-medium text-gray-600">Education</div>
              </div>
              <div className="text-center group">
                <div className="w-16 h-16 mx-auto bg-indigo-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-indigo-200 transition-colors">
                  <svg className="w-8 h-8 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V7l-7-5z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="text-sm font-medium text-gray-600">Retail</div>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Ready to get started?
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
                Transform your customer service with AI-powered chatbots.
                Get started in minutes with our professional tools and free RAG system.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* Contact Information */}
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900 mb-6">Get in touch</h3>
                  <div className="space-y-6">
                    <div className="group flex items-start">
                      <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-blue-200 transition-colors">
                        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 mb-1">Try our AI Agent</div>
                        <div className="text-gray-600 mb-2">Experience intelligent customer service in action</div>
                        <a
                          href="https://axieagent.netlify.app"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-700 text-sm font-medium inline-flex items-center group"
                        >
                          axieagent.netlify.app
                          <ExternalLink className="w-4 h-4 ml-1 group-hover:scale-110 transition-transform" />
                        </a>
                      </div>
                    </div>

                    <div className="group flex items-start">
                      <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-green-200 transition-colors">
                        <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 mb-1">Upload your files</div>
                        <div className="text-gray-600 mb-2">Free RAG system for your company knowledge</div>
                        <a
                          href="https://ragsystem.axiestudio.se/"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-green-600 hover:text-green-700 text-sm font-medium inline-flex items-center group"
                        >
                          ragsystem.axiestudio.se
                          <ExternalLink className="w-4 h-4 ml-1 group-hover:scale-110 transition-transform" />
                        </a>
                      </div>
                    </div>

                    <div className="group flex items-start">
                      <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-gray-200 transition-colors">
                        <Github className="w-6 h-6 text-gray-600" />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 mb-1">View source code</div>
                        <div className="text-gray-600 mb-2">Open source Langflow implementation</div>
                        <a
                          href="https://github.com/OGGsd/Vercel-Frontend-"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-600 hover:text-gray-700 text-sm font-medium inline-flex items-center group"
                        >
                          github.com/OGGsd/Vercel-Frontend-
                          <ExternalLink className="w-4 h-4 ml-1 group-hover:scale-110 transition-transform" />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Support Information */}
                <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-4">Need help?</h4>
                  <div className="space-y-3 text-sm text-gray-600">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                      24/7 technical support available
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Comprehensive documentation
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                      Community forum and resources
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Start Guide */}
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-200">
                <h3 className="text-2xl font-semibold text-gray-900 mb-6">Quick start guide</h3>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 flex-shrink-0">
                      1
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 mb-2">Try the AI Agent</div>
                      <div className="text-gray-600 text-sm mb-3">Experience our chatbot in action with real conversations</div>
                      <button
                        onClick={() => window.open('https://axieagent.netlify.app', '_blank')}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        Launch demo →
                      </button>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 flex-shrink-0">
                      2
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 mb-2">Upload your data</div>
                      <div className="text-gray-600 text-sm mb-3">Use our free RAG system to add your company knowledge</div>
                      <button
                        onClick={() => window.open('https://ragsystem.axiestudio.se/', '_blank')}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        Upload files →
                      </button>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 flex-shrink-0">
                      3
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 mb-2">Build your workflow</div>
                      <div className="text-gray-600 text-sm mb-3">Use our visual builder to create custom flows</div>
                      <button
                        onClick={() => document.getElementById('workflow')?.scrollIntoView({ behavior: 'smooth' })}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        View builder →
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-blue-200">
                  <button
                    onClick={() => window.open('https://axieagent.netlify.app', '_blank')}
                    className="w-full bg-black text-white px-6 py-4 rounded-lg hover:bg-gray-800 transition-colors font-semibold text-lg transform hover:-translate-y-0.5 hover:shadow-lg"
                  >
                    Start building now
                  </button>
                  <div className="text-center mt-3 text-xs text-gray-500">
                    Free to start • No credit card required
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Contact Section */}
      <section id="contact" className="bg-gray-50 py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Ready to get started?
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                Transform your customer service with AI-powered chatbots.
                Get in touch to discuss your specific needs.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-6">Get in touch</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                      <ExternalLink className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Try our AI Agent</div>
                      <a
                        href="https://axieagent.netlify.app"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-700 text-sm"
                      >
                        axieagent.netlify.app
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                      <div className="w-5 h-5 bg-green-600 rounded"></div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Upload your files</div>
                      <a
                        href="https://ragsystem.axiestudio.se/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-green-600 hover:text-green-700 text-sm"
                      >
                        ragsystem.axiestudio.se
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                      <Github className="w-5 h-5 text-gray-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">View source code</div>
                      <a
                        href="https://github.com/OGGsd/Vercel-Frontend-"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-gray-700 text-sm"
                      >
                        github.com/OGGsd/Vercel-Frontend-
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-8 shadow-sm border border-gray-200">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Quick start</h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                      1
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Try the AI Agent</div>
                      <div className="text-sm text-gray-600">Experience our chatbot in action</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                      2
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Upload your data</div>
                      <div className="text-sm text-gray-600">Use our free RAG system</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                      3
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 mb-1">Build your flow</div>
                      <div className="text-sm text-gray-600">Use our visual builder above</div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <button
                    onClick={() => window.open('https://axieagent.netlify.app', '_blank')}
                    className="w-full bg-black text-white px-6 py-3 rounded-md hover:bg-gray-800 transition-colors font-medium"
                  >
                    Start building now
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            {/* Brand Section */}
            <div className="md:col-span-2">
              <div className="flex items-center mb-6">
                <img src="/logo.jpg" alt="Axie Studio" className="h-10 w-10 mr-4" />
                <div>
                  <span className="text-2xl font-bold text-white">Axie Studio</span>
                  <div className="text-sm text-gray-400">AI Customer Service Platform</div>
                </div>
              </div>
              <p className="text-gray-400 leading-relaxed mb-6 max-w-md">
                Build intelligent customer service chatbots with company data integration,
                calendar bookings, and our free RAG system. Transform your customer experience
                with AI that understands your business.
              </p>
              <div className="flex items-center space-x-4">
                <a
                  href="https://github.com/OGGsd/Vercel-Frontend-"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors group"
                >
                  <Github className="w-5 h-5 text-gray-400 group-hover:text-white" />
                </a>
                <div className="w-px h-6 bg-gray-700"></div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-400">Live and operational</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-semibold text-white mb-4">Quick Links</h4>
              <ul className="space-y-3">
                <li>
                  <a href="#features" className="text-gray-400 hover:text-white transition-colors text-sm">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#workflow" className="text-gray-400 hover:text-white transition-colors text-sm">
                    Workflow Builder
                  </a>
                </li>
                <li>
                  <a href="#metrics" className="text-gray-400 hover:text-white transition-colors text-sm">
                    Performance
                  </a>
                </li>
                <li>
                  <a href="#contact" className="text-gray-400 hover:text-white transition-colors text-sm">
                    Get Started
                  </a>
                </li>
              </ul>
            </div>

            {/* Tools & Resources */}
            <div>
              <h4 className="font-semibold text-white mb-4">Tools & Resources</h4>
              <ul className="space-y-3">
                <li>
                  <a
                    href="https://axieagent.netlify.app"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-white transition-colors text-sm flex items-center group"
                  >
                    AI Agent Demo
                    <ExternalLink className="w-3 h-3 ml-1 group-hover:scale-110 transition-transform" />
                  </a>
                </li>
                <li>
                  <a
                    href="https://ragsystem.axiestudio.se/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-white transition-colors text-sm flex items-center group"
                  >
                    RAG System
                    <ExternalLink className="w-3 h-3 ml-1 group-hover:scale-110 transition-transform" />
                  </a>
                </li>
                <li>
                  <a
                    href="https://github.com/OGGsd/Vercel-Frontend-"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-white transition-colors text-sm flex items-center group"
                  >
                    Source Code
                    <ExternalLink className="w-3 h-3 ml-1 group-hover:scale-110 transition-transform" />
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                    Documentation
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="pt-8 border-t border-gray-800">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center space-x-6 mb-4 md:mb-0">
                <p className="text-sm text-gray-400">
                  © 2024 Axie Studio. All rights reserved.
                </p>
                <div className="hidden md:flex items-center space-x-4 text-xs text-gray-500">
                  <span>Privacy Policy</span>
                  <span>•</span>
                  <span>Terms of Service</span>
                  <span>•</span>
                  <span>Support</span>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-xs text-gray-500">
                  Built with Langflow • Powered by AI
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-gray-400">All systems operational</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
