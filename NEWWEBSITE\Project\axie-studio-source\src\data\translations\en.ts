export const en = {
  // Navigation
  nav: {
    booking: "Booking System",
    websites: "Websites",
    apps: "Apps", 
    commerce: "E-commerce",
    services: "Pricing",
    contact: "Contact",
    bookTime: "Book Time",
    login: "Login",
    home: "Home",
    chat: "Chat",
    menu: "Menu",
    changeLanguage: "Change language",
    selectLanguage: "Select Language",
    chooseLanguage: "Choose your preferred language",
    searchLanguages: "Search languages...",
    recent: "Recent",
    noLanguagesFound: "No languages found matching",
    globalExcellence: "Global Excellence",
    globalMessage: "We serve clients worldwide with localized experiences in {{count}} languages, demonstrating our commitment to global accessibility."
  },
  
  // Hero Section
  hero: {
    badge: "#1 in the Industry",
    title: "Build, Book, Automate:",
    subtitle: "Your Digital Success, Simplified.",
    description: "We don't just create digital solutions - we build relationships and deliver results that drive your business forward with heart and passion.",
    cta1: "Let's talk about your project",
    cta2: "Explore our solutions",
    stats: {
      customers: "Trusted by Many",
      uptime: "Uptime",
      service: "Personal Service"
    }
  },
  
  // AI Agent Section
  aiAgent: {
    badge: "AI AGENT",
    title: "Meet Your AI Assistant",
    subtitle: "Experience the future of customer service with our intelligent AI agent that understands your business and helps your customers 24/7.",
    features: {
      availability: {
        title: "24/7 Availability",
        description: "Always ready to help your customers, no matter the time or day."
      },
      responses: {
        title: "Instant Responses",
        description: "Lightning-fast answers to customer questions and inquiries."
      },
      learning: {
        title: "Smart Learning",
        description: "Continuously learns and improves from every interaction."
      }
    },
    status: {
      online: "Live & Ready to Help",
      poweredBy: "Powered by AI",
      loading: "Loading AI Agent..."
    }
  },
  
  // Booking Section
  booking: {
    badge: "Industry-Leading Booking System",
    title: "Advanced Booking System",
    subtitle: "Our booking system is designed to automate and simplify the entire booking process, from first contact to completed service.",
    features: {
      scheduling: {
        title: "Intelligent Scheduling",
        description: "Automatic management of times, resources and staff with conflict detection."
      },
      realtime: {
        title: "Real-time Booking",
        description: "Customers can book directly online with immediate confirmation and synchronization."
      },
      crm: {
        title: "Customer Management",
        description: "Complete CRM with customer history, preferences and automatic communication."
      },
      payments: {
        title: "Payment Integration",
        description: "Secure payments with Stripe, Swish and other popular payment methods."
      },
      reminders: {
        title: "Automatic Reminders",
        description: "SMS and email reminders that reduce no-shows by up to 80%."
      },
      analytics: {
        title: "Analytics & Reports",
        description: "Detailed insights about bookings, revenue and customer trends."
      }
    },
    industries: {
      title: "Perfect for all industries",
      subtitle: "Hair salons, doctors, consultants, restaurants, fitness centers, and many more",
      list: ["Healthcare", "Beauty & Wellness", "Consulting Services", "Restaurants", "Fitness", "Education"]
    }
  },
  
  // Website Section
  websites: {
    badge: "Industry-Leading Websites",
    title: "Websites that Impress",
    subtitle: "We don't just create websites - we build digital experiences that convert visitors into customers and drive your business forward.",
    features: {
      design: {
        title: "Custom Design",
        description: "Unique websites designed specifically for your brand and needs."
      },
      speed: {
        title: "Lightning Fast Loading",
        description: "Optimized performance that gives users the best experience."
      },
      seo: {
        title: "SEO Optimized",
        description: "Built to be visible in search engines and attract more customers."
      },
      mobile: {
        title: "Mobile Responsive",
        description: "Perfect functionality on all devices - mobile, tablet and desktop."
      },
      security: {
        title: "Secure & Reliable",
        description: "SSL encryption and security updates as standard."
      },
      analytics: {
        title: "Analytics & Insights",
        description: "Understand your visitors with detailed reports and statistics."
      }
    },
    cta: {
      title: "From Idea to Reality",
      description: "Our team of experts handles everything from design and development to launch and maintenance. You focus on your business - we handle the technology.",
      features: [
        "Complete project management from start to finish",
        "Continuous support and maintenance",
        "Scalable architecture that grows with your business"
      ],
      deliveryTime: "14 days",
      deliveryText: "Average delivery time for a complete website",
      button: "Start your project"
    },
    videoFallback: "Your browser does not support the video tag."
  },
  
  // Apps Section
  apps: {
    title: "Mobile Apps for All Platforms",
    subtitle: "We develop modern mobile apps that work perfectly on both iOS and Android. From idea to publication in App Store and Google Play Store.",
    features: {
      performance: {
        title: "Lightning Fast Performance",
        description: "Optimized for speed with advanced caching and compression."
      },
      crossPlatform: {
        title: "Cross-Platform",
        description: "Works perfectly on iOS, Android and desktop without extra development."
      },
      notifications: {
        title: "Push Notifications",
        description: "Stay connected with customers through smart and relevant notifications."
      },
      distribution: {
        title: "App Store Distribution",
        description: "Published in both Google Play Store and Apple App Store."
      },
      native: {
        title: "Native App Feel",
        description: "Gives users a genuine app experience with smooth animations."
      },
      security: {
        title: "Secure & Reliable",
        description: "Secure transactions and data protection according to the highest standards."
      }
    },
    cta: {
      title: "From App Idea to App Store",
      description: "We handle the entire process - from design and development to publication in both Google Play Store and Apple App Store.",
      features: [
        "One codebase for all platforms",
        "Automatic updates",
        "Lower development costs",
        "Faster time-to-market",
        "Complete App Store optimization"
      ],
      button: "Start your app"
    },
    videoFallback: "Your browser does not support the video tag."
  },
  
  // Commerce Section
  commerce: {
    title: "E-commerce Solutions",
    subtitle: "Create a professional webshop that converts visitors into customers. Our e-commerce solutions are optimized for sales and user experience.",
    features: {
      webshop: {
        title: "Complete Webshop",
        description: "Fully functional e-commerce solution with shopping cart, checkout and order management."
      },
      products: {
        title: "Product Management",
        description: "Easy to add, edit and organize products with images and variants."
      },
      payments: {
        title: "Secure Payments",
        description: "Integration with Stripe, Klarna, Swish and other popular payment methods."
      },
      shipping: {
        title: "Delivery Options",
        description: "Flexible shipping solutions with automatic price calculation and tracking."
      },
      analytics: {
        title: "Sales Analytics",
        description: "Detailed reports on sales, popular products and customer trends."
      },
      security: {
        title: "Security & GDPR",
        description: "Full GDPR compliance and secure transactions with SSL encryption."
      }
    },
    advanced: {
      title: "Advanced Marketing Tools",
      subtitle: "Our e-commerce solutions include powerful tools to automate marketing, manage customer relationships and maximize your sales.",
      features: {
        crm: {
          title: "CRM & Customer Management",
          description: "Complete customer management with segmentation and personalization"
        },
        marketing: {
          title: "Automated Marketing",
          description: "Email and SMS campaigns that convert leads into customers"
        },
        booking: {
          title: "Integrated Booking System",
          description: "Combine e-commerce with bookings for services and consultations"
        },
        analytics: {
          title: "Advanced Analytics",
          description: "Deep insights into customer journeys and sales trends"
        },
        leads: {
          title: "Lead Generation",
          description: "Powerful tools to capture and convert potential customers"
        },
        automation: {
          title: "Automation",
          description: "Automate repetitive tasks and optimize workflows"
        }
      }
    },
    cta: {
      title: "Everything you need to sell online",
      features: [
        "Responsive design that works on all devices",
        "SEO optimized for better visibility in search engines",
        "Automatic inventory management and notifications",
        "Customer accounts with order history and favorites",
        "Discount codes and campaign tools",
        "Integrated CRM and marketing automation"
      ],
      pricing: "From 10,995 SEK setup fee + 895 SEK/month you get a complete e-commerce solution with everything you need to start selling online.",
      button: "Get started now"
    }
  },
  
  // Services Section
  services: {
    badge: "Our Digital Solutions",
    title: "Choose Your Digital Journey",
    subtitle: "All our packages include professional support, regular updates, and personal service that helps your business grow.",
    
    website: {
      title: "Website",
      description: "Professional website that converts visitors into customers",
      badge: "Perfect start",
      features: [
        "Responsive web design",
        "SEO optimization",
        "Contact form",
        "Social media integration",
        "Google Analytics",
        "SSL certificate",
        "Booking system",
        "Mobile app"
      ]
    },
    
    commerce: {
      title: "Commerce",
      description: "Complete e-commerce solution with webshop and payments",
      badge: "Best for sales",
      features: [
        "Everything in the Website package",
        "E-commerce functionality",
        "Product management",
        "Payment processing",
        "Order management",
        "Customer accounts",
        "Inventory management",
        "Discount codes"
      ]
    },
    
    booking: {
      title: "Booking System", 
      description: "Advanced booking system with automation and CRM",
      badge: "Most popular",
      features: [
        "Everything in the Website package",
        "Advanced booking system",
        "Payment integrations",
        "Automatic reminders",
        "Customer management (CRM)",
        "Calendar synchronization",
        "SMS notifications",
        "Reports & analytics"
      ]
    },
    
    complete: {
      title: "Complete",
      description: "All-in-one solution with app, webshop, and booking system",
      badge: "Best value",
      features: [
        "Everything in previous packages",
        "Dedicated mobile app",
        "App Store publication*",
        "Priority support 24/7",
        "Unlimited bookings",
        "Automated marketing",
        "Advanced CRM & Analytics",
        "White-label solution"
      ],
      info: "For Apple and Google Store publishing additional costs apply beyond what is stated"
    },
    
    cta: {
      title: "Unsure which package suits you?",
      description: "Book a free consultation and we'll help you find the perfect solution for your business.",
      button: "Book free consultation"
    },
    
    fineprint: {
      appStore: "App Store publication: For Apple and Google Store publishing additional costs apply beyond what is stated",
      domain: "Domain name is included in the price",
      vat: "All prices exclude VAT"
    }
  },
  
  // FAQ Section
  faq: {
    badge: "Frequently Asked Questions",
    title: "Questions & Answers",
    subtitle: "Here you'll find answers to the most common questions about our services. Have other questions? Feel free to contact us!",
    questions: [
      {
        question: "What does a website from Axie Studio cost?",
        answer: "Our websites start from 8,995 SEK setup fee plus 495 SEK per month. The price includes design, development, hosting, support and regular updates. We also offer more advanced packages with booking systems and e-commerce."
      },
      {
        question: "How long does it take to develop a website?",
        answer: "Average delivery time for a complete website is 14 days. More complex projects like e-commerce or booking systems can take 3-4 weeks depending on scope and specific requirements."
      },
      {
        question: "Is SEO optimization included in your websites?",
        answer: "Yes, all our websites are SEO optimized from the start. We include technical SEO, fast loading times, mobile optimization, structured data and basic content optimization to give you the best possible visibility in search engines."
      },
      {
        question: "Can you help with e-commerce and webshops?",
        answer: "Absolutely! We create complete e-commerce solutions with product management, secure payments, order management and integrated CRM from 10,995 SEK. Our webshops are optimized for conversion and user experience."
      },
      {
        question: "What's included in the monthly fee?",
        answer: "The monthly fee includes hosting, security updates, technical support, backup, monitoring and minor content updates. You also get access to our support portal and priority help when you need it."
      },
      {
        question: "Can I cancel my subscription at any time?",
        answer: "Yes, we have no binding periods. You can cancel your subscription at any time with one month's notice. We believe in delivering such good service that you want to stay voluntarily."
      },
      {
        question: "Do you help with marketing and Google Ads?",
        answer: "Yes, we offer digital marketing as an additional service. This includes Google Ads, Facebook advertising, SEO optimization and content marketing to help you reach more customers online."
      },
      {
        question: "Can you integrate with existing systems?",
        answer: "Absolutely! We have extensive experience integrating websites with existing systems like accounting systems, CRM, bookkeeping programs and other tools your business already uses."
      }
    ],
    cta: {
      title: "Have more questions?",
      description: "We're happy to answer all your questions about our services and how we can help your business.",
      button: "Contact us"
    }
  },
  
  // Contact Section
  contact: {
    badge: "Let's talk",
    title: "Ready to Transform Your Business?",
    description: "We love meeting new people and hearing about exciting projects. Let's have a coffee (virtual or physical) and talk about how we can help you grow.",
    
    email: {
      title: "Send an email",
      subtitle: "We respond within 2 hours",
      addresses: [
        { email: "<EMAIL>", label: "General inquiries" },
        { email: "<EMAIL>", label: "Support & help" }
      ]
    },
    
    phone: {
      title: "Call us directly",
      subtitle: "Weekdays 9-17",
      numbers: [
        { number: "+**************", label: "Sweden" },
        { number: "+63 ************", label: "Philippines" }
      ]
    },
    
    location: {
      title: "Meet in person",
      subtitle: "Or virtually via video",
      address: "Jönköping, Sweden"
    },
    
    consultation: {
      title: "Free Consultation over Coffee ☕",
      description: "We always offer a free initial consultation where we get to know you, your business, and your dreams. No sales pitches - just genuine conversations about how we can help you.",
      features: {
        duration: "30-60 minutes",
        free: "Completely free",
        atmosphere: "Relaxed atmosphere"
      },
      buttons: {
        book: "Book time online",
        email: "Send email", 
        call: "Call now"
      }
    }
  },
  
  // Footer Section
  footer: {
    tagline: "Digital Excellence",
    slogan: "Build, Book, Automate: Your Digital Success, Simplified.",
    description: "We help businesses digitize and automate their processes with modern websites, booking systems and e-commerce solutions that drive real growth.",
    services: {
      title: "Services",
      items: [
        { name: "Booking System", href: "#booking" },
        { name: "Websites", href: "#websites" },
        { name: "Mobile Apps", href: "#apps" },
        { name: "E-commerce", href: "#commerce" }
      ]
    },
    contact: {
      title: "Contact",
      email: {
        label: "Email",
        value: "<EMAIL>"
      },
      phone: {
        label: "Phone",
        value: "+**************"
      },
      location: {
        label: "Location",
        value: "Jönköping, Sweden"
      }
    },
    legal: {
      copyright: "© 2025 Axie Studio. All rights reserved.",
      links: [
        { name: "Privacy Policy", href: "/privacy" },
        { name: "Terms of Service", href: "/terms" }
      ],
      madeWith: "Made with",
      madeIn: "in Sweden"
    }
  },
  
  // Privacy Policy
  privacy: {
    badge: "Privacy Policy",
    title: "Privacy Policy",
    subtitle: "We value your privacy and are transparent about how we handle your personal information. This policy explains how we collect, use, and protect your information.",
    lastUpdated: "Last updated",
    
    dataCollection: {
      title: "Collection of Personal Information",
      content: [
        "We collect personal information that you voluntarily provide to us when you contact us, book our services, or use our website. This includes name, email address, phone number, and company information.",
        "We also automatically collect technical information through cookies and similar technologies, such as IP address, browser type, visit times, and page views to improve user experience.",
        "All collection of personal information is done in accordance with GDPR and Swedish data protection legislation."
      ]
    },
    
    dataUsage: {
      title: "Use of Personal Information",
      content: [
        "We use your personal information to provide our services, communicate with you, answer your questions, and improve our services.",
        "We may use your information to send you relevant updates about our services, but only if you have given your consent.",
        "We never share your personal information with third parties without your explicit consent, except when required by law."
      ]
    },
    
    dataSecurity: {
      title: "Data Security",
      content: [
        "We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, loss, or misuse.",
        "All data transmission occurs via encrypted connections (SSL/TLS) and we use secure servers for data storage.",
        "Our employees and partners who have access to personal information are bound by confidentiality agreements."
      ]
    },
    
    userRights: {
      title: "Your Rights",
      content: [
        "You have the right to request access to, correction of, or deletion of your personal information. You can also request restriction of processing or object to processing.",
        "You have the right to data portability, which means you can request to receive your data in a structured, commonly used format.",
        "You can withdraw your consent to processing of personal information at any time and you have the right to file a complaint with the Data Protection Authority."
      ]
    },
    
    cookies: {
      title: "Cookies and Tracking",
      content: [
        "We use cookies to improve the functionality of our website and to analyze how the website is used.",
        "You can control and manage cookies through your browser settings. Note that disabling cookies may affect the functionality of our website.",
        "We use Google Analytics to understand how visitors interact with our website, but all data is anonymized."
      ]
    },
    
    contact: {
      title: "Contact Us",
      content: [
        "If you have questions about this privacy policy or how we handle your personal information, contact <NAME_EMAIL>.",
        "We strive to respond to all requests within 30 days and will keep you informed of any changes to this policy.",
        "Axie Studio, Jönköping, Sweden."
      ]
    },
    
    cta: {
      title: "Questions about Privacy?",
      description: "We are transparent about how we handle your data and are happy to answer any questions you have about your privacy.",
      button: "Contact us"
    }
  },
  
  // Terms of Service
  terms: {
    badge: "Terms of Service",
    title: "Terms of Service",
    subtitle: "These terms govern the use of our services and website. By using our services, you accept these terms.",
    lastUpdated: "Last updated",
    
    acceptance: {
      title: "Acceptance of Terms",
      content: [
        "By using Axie Studio's services or website, you agree to be bound by these terms of service and our privacy policy.",
        "If you do not accept these terms, you should not use our services. We reserve the right to update these terms at any time.",
        "Continued use of our services after changes means you accept the updated terms."
      ]
    },
    
    services: {
      title: "Our Services",
      content: [
        "Axie Studio provides web development, booking systems, e-commerce solutions, and related digital services.",
        "We strive to deliver services of the highest quality but cannot guarantee that services will always be error-free or uninterrupted.",
        "We reserve the right to modify, discontinue, or cease our services with reasonable notice."
      ]
    },
    
    payment: {
      title: "Payment and Billing",
      content: [
        "Payment for our services shall be made according to agreed terms. Setup fees are paid before project start and monthly fees are charged in advance.",
        "In case of late payment, we reserve the right to apply late interest according to applicable law and to temporarily suspend services.",
        "All prices are exclusive of VAT unless otherwise stated. Price changes are communicated with at least 30 days' notice."
      ]
    },
    
    userResponsibilities: {
      title: "User Responsibilities",
      content: [
        "You are responsible for providing correct and current information needed to deliver our services.",
        "You may not use our services for illegal purposes or in ways that could harm our business or other users.",
        "You are responsible for ensuring that all content you provide does not infringe third party rights."
      ]
    },
    
    limitations: {
      title: "Liability Limitations",
      content: [
        "Axie Studio's liability is limited to the amount paid for the specific services that caused the damage.",
        "We are not liable for indirect damages, lost profits, or other consequential damages that may arise from the use of our services.",
        "We are not liable for damages caused by third parties, technical errors beyond our control, or force majeure."
      ]
    },
    
    termination: {
      title: "Termination",
      content: [
        "You can terminate our services at any time with one month's notice. We can terminate services in case of material breach of contract.",
        "Upon termination, we will provide reasonable assistance for transfer of data and materials, but are not responsible for costs related to this.",
        "Certain provisions of these terms will continue to apply even after termination, including liability limitations and confidentiality."
      ]
    },
    
    cta: {
      title: "Questions about the Terms?",
      description: "If you have questions about our terms of service or need clarifications, please feel free to contact us.",
      button: "Contact us"
    }
  },
  
  // Common
  common: {
    startPrice: "Setup fee",
    monthly: "per month",
    getStarted: "Get started",
    learnMore: "Learn more",
    bookConsultation: "Book consultation",
    freeConsultation: "Free consultation",
    noCommitment: "No commitments",
    support247: "24/7 Support",
    fastDelivery: "Fast delivery",
    readMore: "Read more",
    showLess: "Show less",
    loading: "Loading...",
    error: "An error occurred",
    success: "Success!",
    close: "Close",
    open: "Open",
    save: "Save",
    cancel: "Cancel",
    confirm: "Confirm",
    delete: "Delete",
    edit: "Edit",
    add: "Add",
    remove: "Remove",
    update: "Update",
    create: "Create",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    next: "Next",
    previous: "Previous",
    first: "First",
    last: "Last",
    backToHome: "Back to homepage"
  },
  
  // Language Switcher
  bookingModal: {
    title: "Book Consultation",
    subtitle: "Choose a time that suits you",
    features: {
      duration: "30-60 min",
      free: "Completely free",
      personal: "Personal service"
    },
    loading: "Loading booking calendar...",
    iframeTitle: "Book consultation"
  }
};